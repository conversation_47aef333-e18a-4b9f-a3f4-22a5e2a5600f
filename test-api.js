// Quick API test script
// Run this in browser console to test API endpoints

async function testAPI() {
  console.log('Testing API endpoints with branch filtering...')
  
  try {
    // Test students API
    console.log('Testing /api/students...')
    const studentsResponse = await fetch('/api/students?branch=main')
    const studentsData = await studentsResponse.json()
    console.log('Students:', studentsData)
    
    // Test groups API
    console.log('Testing /api/groups...')
    const groupsResponse = await fetch('/api/groups?branch=main')
    const groupsData = await groupsResponse.json()
    console.log('Groups:', groupsData)
    
    // Test payments API
    console.log('Testing /api/payments...')
    const paymentsResponse = await fetch('/api/payments?branch=main')
    const paymentsData = await paymentsResponse.json()
    console.log('Payments:', paymentsData)
    
    // Test enrollments API
    console.log('Testing /api/enrollments...')
    const enrollmentsResponse = await fetch('/api/enrollments?branch=main')
    const enrollmentsData = await enrollmentsResponse.json()
    console.log('Enrollments:', enrollmentsData)
    
    // Test assessments API
    console.log('Testing /api/assessments...')
    const assessmentsResponse = await fetch('/api/assessments?branch=main')
    const assessmentsData = await assessmentsResponse.json()
    console.log('Assessments:', assessmentsData)
    
    console.log('All API tests completed!')
    
  } catch (error) {
    console.error('API test failed:', error)
  }
}

// Run the test
testAPI()
